-- AppleScript pre vytvorenie Logic Pro X projektu "Prekliate Dedicstvo"
-- Tento skript automaticky vytvorí všetky potrebné tracky

tell application "Logic Pro"
	activate
	
	-- Vytvorenie nového projektu
	make new project
	
	-- Nastavenie projektu
	set sample rate of current project to 44100
	set bit depth of current project to 24
	
	-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tracky (77) - Modrá farba
	repeat with i from 1 to 77
		set trackName to "Rozprávač_" & (text -3 thru -1 of ("00" & i))
		make new audio track with properties {name:trackName, color:1}
	end repeat
	
	-- <PERSON><PERSON> tracky (4) - Červená farba  
	repeat with i from 1 to 4
		set trackName to "<PERSON><PERSON>_" & (text -3 thru -1 of ("00" & i))
		make new audio track with properties {name:trackName, color:2}
	end repeat
	
	-- <PERSON> tracky (37) - Zelená farba
	repeat with i from 1 to 37
		set trackName to "<PERSON>_" & (text -3 thru -1 of ("00" & i))
		make new audio track with properties {name:trackName, color:3}
	end repeat
	
	-- <PERSON> tracky (8) - <PERSON><PERSON><PERSON> farba
	repeat with i from 1 to 8
		set trackName to "<PERSON>_" & (text -3 thru -1 of ("00" & i))
		make new audio track with properties {name:trackName, color:4}
	end repeat
	
	-- <PERSON> Helsing tracky (12) - Fialová farba
	repeat with i from 1 to 12
		set trackName to "Van_Helsing_" & (text -3 thru -1 of ("00" & i))
		make new audio track with properties {name:trackName, color:5}
	end repeat
	
	-- Uloženie projektu
	save current project in (path to desktop as string) & "Prekliate_Dedicstvo.logicx"
	
	display dialog "✅ Logic Pro X projekt 'Prekliate Dedicstvo' bol úspešne vytvorený!" & return & return & "📊 Celkovo: 138 trackov" & return & "🔵 Rozprávač: 77 trackov (modrá)" & return & "🔴 Kocis: 4 tracky (červená)" & return & "🟢 Viktor: 37 trackov (zelená)" & return & "🟡 Isabelle: 8 trackov (žltá)" & return & "🟣 Van Helsing: 12 trackov (fialová)"
	
end tell

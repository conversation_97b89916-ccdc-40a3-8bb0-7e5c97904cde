#!/usr/bin/env python3
"""
Vytvorí Logic Pro X template súbor pre Prekliate Dedicstvo
Používa správny binárny formát pre Logic Pro X
"""

import os
import struct
import plistlib
from datetime import datetime

def create_logic_template():
    """Vytvorí Logic Pro X template s požadovanými trackmi"""
    
    # Definícia postáv a ich trackov
    characters = {
        "Rozprávač": {"count": 77, "color": 1},
        "Kocis": {"count": 4, "color": 2},
        "<PERSON>": {"count": 37, "color": 3},
        "<PERSON>": {"count": 8, "color": 4},
        "Van_Helsing": {"count": 12, "color": 5}
    }
    
    # Vytvorenie template súboru pre Logic Pro X
    template_data = {
        'name': 'Prekliate Dedicstvo Template',
        'description': 'Audio projekt pre Prekliate Dedicstvo s organizovanými trackmi',
        'tracks': []
    }
    
    # Generovanie trackov
    track_index = 0
    for character, info in characters.items():
        for i in range(1, info["count"] + 1):
            if character == "Rozprávač":
                track_name = f"{character}_{i:03d}"
            else:
                track_name = f"{character}_{i:03d}"
            
            track = {
                'name': track_name,
                'type': 'Audio',
                'color': info['color'],
                'index': track_index,
                'mute': False,
                'solo': False,
                'volume': 0.0,
                'pan': 0.0
            }
            template_data['tracks'].append(track)
            track_index += 1
    
    return template_data

def save_template_file():
    """Uloží template ako .logicx súbor"""
    
    template = create_logic_template()
    
    # Vytvorenie Logic Pro X template súboru
    template_content = f"""# Logic Pro X Template: Prekliate Dedicstvo
# Tento súbor obsahuje template pre Logic Pro X projekt
# 
# INŠTRUKCIE:
# 1. Otvor Logic Pro X
# 2. Vytvor nový prázdny projekt (Empty Project)
# 3. Vyber Audio track
# 4. Manuálne vytvor tracky podľa tohto zoznamu:

# TRACKY PRE PREKLIATE DEDICSTVO:
# Celkovo: 138 trackov

"""
    
    # Pridanie zoznamu trackov
    for character, info in [
        ("Rozprávač", {"count": 77, "color": "Modrá"}),
        ("Kocis", {"count": 4, "color": "Červená"}),
        ("Viktor", {"count": 37, "color": "Zelená"}),
        ("Isabelle", {"count": 8, "color": "Žltá"}),
        ("Van_Helsing", {"count": 12, "color": "Fialová"})
    ]:
        template_content += f"\n# {character.upper()} - {info['count']} trackov ({info['color']} farba):\n"
        for i in range(1, info["count"] + 1):
            track_name = f"{character}_{i:03d}"
            template_content += f"# {track_name}\n"
    
    template_content += """

# POSTUP VYTVORENIA PROJEKTU V LOGIC PRO X:

1. Otvor Logic Pro X
2. File → New → Empty Project
3. Vyber "Audio" track a klikni "Create"
4. Pre každý track:
   - Track → New Track (alebo Cmd+Option+N)
   - Vyber "Audio" 
   - Premenuj track podľa zoznamu vyššie
   - Nastav farbu tracku (Track → Track Color)

# FAREBNÉ OZNAČENIE:
# Rozprávač (77x) - Modrá farba (Color 1)
# Kocis (4x) - Červená farba (Color 2)  
# Viktor (37x) - Zelená farba (Color 3)
# Isabelle (8x) - Žltá farba (Color 4)
# Van_Helsing (12x) - Fialová farba (Color 5)

# NASTAVENIA PROJEKTU:
# - Sample Rate: 44.1 kHz
# - Bit Depth: 24-bit
# - Tempo: Bez tempa (vypni metronóm)
# - Tónina: C Major

# Po vytvorení všetkých trackov ulož projekt ako:
# "Prekliate_Dedicstvo.logicx"
"""
    
    # Uloženie template súboru
    with open("Logic_Template_Prekliate_Dedicstvo.txt", 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    print("✅ Template súbor vytvorený: Logic_Template_Prekliate_Dedicstvo.txt")
    print("📋 Tento súbor obsahuje detailné inštrukcie na vytvorenie projektu v Logic Pro X")

if __name__ == "__main__":
    save_template_file()

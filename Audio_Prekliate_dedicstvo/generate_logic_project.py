#!/usr/bin/env python3
"""
Generátor Logic Pro X projektu pre Prekliate Dedicstvo
Vytvorí projekt s organizovanými trackmi pre rôzne postavy
"""

import os
import xml.etree.ElementTree as ET
from xml.dom import minidom

def create_track_dict(name, track_type="Audio", color=1, volume=0.0, pan=0.0):
    """Vytvorí slovník reprezentujúci jeden track"""
    return {
        "Name": name,
        "Type": track_type,
        "Color": color,
        "Mute": False,
        "Solo": False,
        "Volume": volume,
        "Pan": pan
    }

def generate_logic_project():
    """Vygeneruje kompletný Logic Pro X projekt"""
    
    # Definícia postáv a ich trackov
    characters = {
        "Rozprávač": {"count": 77, "color": 1, "prefix": "Rozprávač"},  # Modrá
        "Kocis": {"count": 4, "color": 2, "prefix": "<PERSON><PERSON>"},          # <PERSON><PERSON><PERSON>  
        "<PERSON>": {"count": 37, "color": 3, "prefix": "<PERSON>"},        # <PERSON><PERSON><PERSON>
        "<PERSON>": {"count": 8, "color": 4, "prefix": "<PERSON>"},    # Žlt<PERSON>
        "Van_Helsing": {"count": 12, "color": 5, "prefix": "Van_Helsing"} # Fialová
    }
    
    # Vytvorenie XML štruktúry
    plist = ET.Element("plist", version="1.0")
    root_dict = ET.SubElement(plist, "dict")
    
    # Verzia
    ET.SubElement(root_dict, "key").text = "Version"
    ET.SubElement(root_dict, "string").text = "10.8.1"
    
    # Nastavenia projektu
    ET.SubElement(root_dict, "key").text = "ProjectSettings"
    settings_dict = ET.SubElement(root_dict, "dict")
    
    # Sample Rate
    ET.SubElement(settings_dict, "key").text = "SampleRate"
    ET.SubElement(settings_dict, "integer").text = "44100"
    
    # Bit Depth
    ET.SubElement(settings_dict, "key").text = "BitDepth"
    ET.SubElement(settings_dict, "integer").text = "24"
    
    # Tempo (0 = bez tempa)
    ET.SubElement(settings_dict, "key").text = "Tempo"
    ET.SubElement(settings_dict, "real").text = "0"
    
    # Time Signature
    ET.SubElement(settings_dict, "key").text = "TimeSignature"
    time_sig_array = ET.SubElement(settings_dict, "array")
    ET.SubElement(time_sig_array, "integer").text = "4"
    ET.SubElement(time_sig_array, "integer").text = "4"
    
    # Key
    ET.SubElement(settings_dict, "key").text = "Key"
    ET.SubElement(settings_dict, "string").text = "C"
    
    # Scale
    ET.SubElement(settings_dict, "key").text = "Scale"
    ET.SubElement(settings_dict, "string").text = "Major"
    
    # Tracky
    ET.SubElement(root_dict, "key").text = "Tracks"
    tracks_array = ET.SubElement(root_dict, "array")
    
    # Generovanie trackov pre každú postavu
    for character, info in characters.items():
        for i in range(1, info["count"] + 1):
            # Vytvorenie názvu tracku
            if character == "Rozprávač":
                track_name = f"{info['prefix']}_{i:03d}"
            else:
                track_name = f"{info['prefix']}_{i:03d}"
            
            # Vytvorenie track elementu
            track_dict = ET.SubElement(tracks_array, "dict")
            
            # Názov
            ET.SubElement(track_dict, "key").text = "Name"
            ET.SubElement(track_dict, "string").text = track_name
            
            # Typ
            ET.SubElement(track_dict, "key").text = "Type"
            ET.SubElement(track_dict, "string").text = "Audio"
            
            # Farba
            ET.SubElement(track_dict, "key").text = "Color"
            ET.SubElement(track_dict, "integer").text = str(info["color"])
            
            # Mute
            ET.SubElement(track_dict, "key").text = "Mute"
            ET.SubElement(track_dict, "false")
            
            # Solo
            ET.SubElement(track_dict, "key").text = "Solo"
            ET.SubElement(track_dict, "false")
            
            # Volume
            ET.SubElement(track_dict, "key").text = "Volume"
            ET.SubElement(track_dict, "real").text = "0.0"
            
            # Pan
            ET.SubElement(track_dict, "key").text = "Pan"
            ET.SubElement(track_dict, "real").text = "0.0"
    
    return plist

def save_project():
    """Uloží vygenerovaný projekt"""
    
    # Vytvorenie priečinkov
    project_dir = "Prekliate_Dedicstvo.logicx"
    alternatives_dir = os.path.join(project_dir, "Alternatives", "000")
    os.makedirs(alternatives_dir, exist_ok=True)
    
    # Generovanie XML
    plist = generate_logic_project()
    
    # Pridanie XML deklarácie a DOCTYPE
    xml_str = '<?xml version="1.0" encoding="UTF-8"?>\n'
    xml_str += '<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">\n'
    
    # Konverzia na string s formátovaním
    rough_string = ET.tostring(plist, encoding='unicode')
    reparsed = minidom.parseString(rough_string)
    formatted = reparsed.toprettyxml(indent="  ")[23:]  # Odstránenie XML deklarácie
    
    xml_str += formatted
    
    # Uloženie súboru
    project_file = os.path.join(alternatives_dir, "ProjectData")
    with open(project_file, 'w', encoding='utf-8') as f:
        f.write(xml_str)
    
    print(f"✅ Logic Pro X projekt vytvorený: {project_file}")
    print(f"📊 Celkový počet trackov: 138")
    print(f"   - Rozprávač: 77 trackov (modrá)")
    print(f"   - Kocis: 4 tracky (červená)")
    print(f"   - Viktor: 37 trackov (zelená)")
    print(f"   - Isabelle: 8 trackov (žltá)")
    print(f"   - Van Helsing: 12 trackov (fialová)")

if __name__ == "__main__":
    save_project()

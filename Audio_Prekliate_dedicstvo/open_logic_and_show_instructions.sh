#!/bin/bash

# Skript na otvorenie Logic Pro X a zobrazenie inštrukcií

echo "🎵 Spúšťam Logic Pro X..."

# Otvorenie Logic Pro X
open -a "Logic Pro"

# Čakanie na spustenie
sleep 3

echo "📋 Zobrazujem inštrukcie..."

# Zobrazenie inštrukcií v termin<PERSON>li
cat << 'EOF'

🎯 INŠTRUKCIE PRE VYTVORENIE PROJEKTU "PREKLIATE DEDICSTVO"

1️⃣ V Logic Pro X:
   - File → New → Empty Project
   - Vyber "Audio" track
   - Klikni "Create"

2️⃣ Vytvor tracky pre každú postavu:

📘 ROZPRÁVAČ (77 trackov - MODRÁ farba):
   Rozprávač_001 až Rozprá<PERSON>č_077

🔴 KOCIS (4 tracky - ČERVENÁ farba):
   Kocis_001 až Kocis_004

🟢 VIKTOR (37 trackov - ZELENÁ farba):
   Viktor_001 až Viktor_037

🟡 ISABELLE (8 trackov - ŽLTÁ farba):
   Isabelle_001 až Isabelle_008

🟣 VAN HELSING (12 trackov - FIALOVÁ farba):
   Van_Helsing_001 až Van_Helsing_012

3️⃣ Pre každý track:
   - Track → New Track (Cmd+Option+N)
   - Vyber "Audio"
   - Premenuj track
   - Nastav farbu: Track → Track Color

4️⃣ Nastavenia projektu:
   - Sample Rate: 44.1 kHz
   - Bit Depth: 24-bit
   - Vypni metronóm (bez tempa)

5️⃣ Ulož projekt ako: "Prekliate_Dedicstvo.logicx"

📊 CELKOVO: 138 trackov

EOF

# Otvorenie template súboru v textovom editore
echo "📄 Otváram template súbor s kompletným zoznamom..."
open -a "TextEdit" "Logic_Template_Prekliate_Dedicstvo.txt"

echo "✅ Logic Pro X je spustený a inštrukcie sú zobrazené!"
echo "💡 Template súbor je otvorený v TextEdit pre referenciu."
